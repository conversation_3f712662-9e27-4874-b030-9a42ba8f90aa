{"version": 3, "file": "user.js", "sources": ["src/stores/modules/user.ts"], "sourcesContent": ["/**\n * 用户状态管理\n */\nimport { ref, computed } from 'vue';\nimport { defineStore } from 'pinia';\nimport type { UserInfo } from '../../types/api';\n\nexport const useUserStore = defineStore('user', () => {\n  // State\n  const profile = ref<UserInfo | null>(null);\n\n  // Getters\n  const isLoggedIn = computed(() => !!profile.value?.token);\n  const token = computed(() => profile.value?.token || '');\n  const userStatus = computed(() => profile.value?.status || 'new');\n  const isApproved = computed(() => profile.value?.status === 'approved');\n  const isPendingReview = computed(() => profile.value?.status === 'pending_review');\n  const isRejected = computed(() => profile.value?.status === 'rejected');\n  const isNew = computed(() => profile.value?.status === 'new');\n\n  // Actions\n  function setProfile(userInfo: UserInfo) {\n    profile.value = userInfo;\n    // 持久化存储\n    uni.setStorageSync('user_profile', userInfo);\n  }\n\n  function updateProfile(updates: Partial<UserInfo>) {\n    if (profile.value) {\n      profile.value = { ...profile.value, ...updates };\n      uni.setStorageSync('user_profile', profile.value);\n    }\n  }\n\n  function clearProfile() {\n    profile.value = null;\n    uni.removeStorageSync('user_profile');\n  }\n\n  function initProfile() {\n    try {\n      const savedProfile = uni.getStorageSync('user_profile');\n      if (savedProfile) {\n        profile.value = savedProfile;\n      }\n    } catch (error) {\n      console.error('初始化用户信息失败:', error);\n    }\n  }\n\n  return {\n    // State\n    profile,\n    // Getters\n    isLoggedIn,\n    token,\n    userStatus,\n    isApproved,\n    isPendingReview,\n    isRejected,\n    isNew,\n    // Actions\n    setProfile,\n    updateProfile,\n    clearProfile,\n    initProfile,\n  };\n});\n"], "names": ["defineStore", "ref", "computed", "uni"], "mappings": ";;AAOa,MAAA,eAAeA,cAAAA,YAAY,QAAQ,MAAM;AAE9C,QAAA,UAAUC,kBAAqB,IAAI;AAGzC,QAAM,aAAaC,cAAAA,SAAS,MAAM;;AAAA,YAAC,GAAC,aAAQ,UAAR,mBAAe;AAAA,GAAK;AACxD,QAAM,QAAQA,cAAAA,SAAS,MAAM;;AAAA,0BAAQ,UAAR,mBAAe,UAAS;AAAA,GAAE;AACvD,QAAM,aAAaA,cAAAA,SAAS,MAAM;;AAAA,0BAAQ,UAAR,mBAAe,WAAU;AAAA,GAAK;AAChE,QAAM,aAAaA,cAAAA,SAAS,MAAM;;AAAA,0BAAQ,UAAR,mBAAe,YAAW;AAAA,GAAU;AACtE,QAAM,kBAAkBA,cAAAA,SAAS,MAAM;;AAAA,0BAAQ,UAAR,mBAAe,YAAW;AAAA,GAAgB;AACjF,QAAM,aAAaA,cAAAA,SAAS,MAAM;;AAAA,0BAAQ,UAAR,mBAAe,YAAW;AAAA,GAAU;AACtE,QAAM,QAAQA,cAAAA,SAAS,MAAM;;AAAA,0BAAQ,UAAR,mBAAe,YAAW;AAAA,GAAK;AAG5D,WAAS,WAAW,UAAoB;AACtC,YAAQ,QAAQ;AAEZC,kBAAAA,MAAA,eAAe,gBAAgB,QAAQ;AAAA,EAC7C;AAEA,WAAS,cAAc,SAA4B;AACjD,QAAI,QAAQ,OAAO;AACjB,cAAQ,QAAQ,EAAE,GAAG,QAAQ,OAAO,GAAG;AACnCA,oBAAAA,MAAA,eAAe,gBAAgB,QAAQ,KAAK;AAAA,IAClD;AAAA,EACF;AAEA,WAAS,eAAe;AACtB,YAAQ,QAAQ;AAChBA,wBAAI,kBAAkB,cAAc;AAAA,EACtC;AAEA,WAAS,cAAc;AACjB,QAAA;AACI,YAAA,eAAeA,cAAAA,MAAI,eAAe,cAAc;AACtD,UAAI,cAAc;AAChB,gBAAQ,QAAQ;AAAA,MAClB;AAAA,aACO,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,oCAAA,cAAc,KAAK;AAAA,IACnC;AAAA,EACF;AAEO,SAAA;AAAA;AAAA,IAEL;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;;"}