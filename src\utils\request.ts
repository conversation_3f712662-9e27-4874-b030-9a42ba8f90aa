/**
 * HTTP请求封装
 * 基于luch-request的统一请求封装
 */
import Request from 'luch-request';
import { useUserStore } from '../stores/modules/user';
import type { ApiResponse } from '../types/api';

// 创建请求实例
const http = new Request({
  baseURL: 'http://127.0.0.1:3000/api/v1', // TODO: 替换为实际的API地址
  timeout: 10000,
  header: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true,
    });

    // 添加token
    const userStore = useUserStore();
    if (userStore.token) {
      config.header.Authorization = `Bearer ${userStore.token}`;
    }

    return config;
  },
  (error) => {
    uni.hideLoading();
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    uni.hideLoading();

    // 检查是否跳过响应拦截器处理
    if (response.config?.custom?.skipResponseInterceptor) {
      return Promise.resolve(response.data);
    }

    // 统一以 HTTP statusCode 为判断依据
    const { statusCode, data } = response as {
      statusCode: number;
      data: any;
    };

    // 2xx/3xx → 视为成功，直接透出 data
    if (statusCode >= 200 && statusCode < 400) {
      return Promise.resolve(data);
    }

    // Token 失效
    if (statusCode === 401) {
      const userStore = useUserStore();
      userStore.clearProfile();
      
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000,
      });
      
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login',
        });
      }, 2000);
      
      return Promise.reject(data);
    }

    // 其他错误（4xx/5xx）
    uni.showToast({
      title: data?.message || '请求失败',
      icon: 'none',
      duration: 2000,
    });
    
    return Promise.reject(data);
  },
  (error) => {
    uni.hideLoading();
    
    console.error('HTTP请求错误:', error);
    
    // 网络错误处理
    let errorMessage = '网络错误，请稍后重试';
    
    if (error.statusCode) {
      switch (error.statusCode) {
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        case 502:
          errorMessage = '网关错误';
          break;
        case 503:
          errorMessage = '服务不可用';
          break;
        case 504:
          errorMessage = '网关超时';
          break;
        default:
          errorMessage = `请求失败 (${error.statusCode})`;
      }
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    });
    
    return Promise.reject(error);
  }
);

export default http;
