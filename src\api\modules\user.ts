/**
 * 用户相关API接口
 */
import http from '../../utils/request';
import type { UserInfo, LoginParams, RegisterParams, WxLoginResponse, Institution, Position } from '../../types/api';

/**
 * 微信登录 - 特殊处理，直接返回数据不经过标准响应拦截器
 */
export function wxLogin(params: LoginParams) {
  return http.post<WxLoginResponse>('/auth/wechat-login', params, {
    custom: {
      skipResponseInterceptor: true
    }
  });
}

/**
 * 获取机构列表
 */
export function getInstitutions() {
  return http.get<Institution[]>('/institutions');
}

/**
 * 获取职位列表
 */
export function getPositions() {
  return http.get<Position[]>('/positions');
}

/**
 * 提交用户注册信息
 */
export function submitUserInfo(params: RegisterParams) {
  return http.post<boolean>('/profile', params);
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<UserInfo>('/profile/me');
}

/**
 * 上传用户头像
 */
export function uploadAvatar(file: File) {
  return http.upload<string>('/user/upload-avatar', { file });
}
