{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<!--\n  疾控考试系统 - 登录页面\n  复选框修复版本 - 2025-06-17T23:48:55\n\n  设计理念：\n  - 医疗健康主题配色（浅蓝+白色，绿蓝渐变）\n  - 垂直居中布局，按钮位于60-70%高度\n  - 清晰的视觉反馈和用户体验\n  - 严格遵循uview-plus组件规范\n\n  修复内容：\n  - 修复复选框勾选标记不显示问题\n  - 改用u-checkbox-group标准组件模式\n  - 优化复选框样式和视觉反馈\n-->\n<template>\n  <view class=\"login-page\">\n    <!-- 背景装饰层 -->\n    <view class=\"background-decoration\">\n      <view class=\"decoration-circle decoration-circle--1\"></view>\n      <view class=\"decoration-circle decoration-circle--2\"></view>\n      <view class=\"decoration-circle decoration-circle--3\"></view>\n    </view>\n\n    <!-- 主要内容容器 -->\n    <view class=\"main-container\">\n      <!-- 顶部品牌区域 -->\n      <view class=\"brand-section\">\n        <view class=\"logo-wrapper\">\n          <view class=\"logo-background\">\n            <u-icon name=\"medical-bag\" size=\"48\" color=\"#ffffff\" />\n          </view>\n        </view>\n        <view class=\"brand-text\">\n          <text class=\"main-title\">疾控考试系统</text>\n          <text class=\"sub-title\">医护任职资格考试平台</text>\n        </view>\n      </view>\n\n      <!-- 登录操作区域 - 位于60%高度 -->\n      <view class=\"login-section\">\n        <!-- 微信登录按钮 -->\n        <view class=\"login-button-wrapper\">\n          <u-button\n            type=\"primary\"\n            text=\"微信授权登录\"\n            icon=\"weixin-fill\"\n            :disabled=\"!agreedToTerms || isLoading\"\n            :loading=\"isLoading\"\n            loadingText=\"登录中...\"\n            :customStyle=\"loginButtonStyle\"\n            shape=\"circle\"\n            size=\"large\"\n            :throttleTime=\"1000\"\n            @click=\"handleWxLogin\"\n          />\n        </view>\n\n        <!-- 协议确认区域 -->\n        <view class=\"agreement-section\">\n          <view class=\"checkbox-container\">\n            <u-checkbox\n              v-model:checked=\"agreedToTerms\"\n              activeColor=\"#4CAF50\"       \n              inactiveColor=\"#e0e0e0\"\n              size=\"20\"\n              iconSize=\"14\"\n              shape=\"circle\"\n              :disabled=\"isLoading\"\n              usedAlone\n              label=\"我已阅读并同意\"\n            />\n          </view>\n          <view class=\"agreement-links\">\n            <text class=\"agreement-link\" @click=\"showUserAgreement\">《用户服务协议》</text>\n            <text class=\"agreement-text\">和</text>\n            <text class=\"agreement-link\" @click=\"showPrivacyPolicy\">《隐私政策》</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 用户协议模态框 -->\n    <u-modal\n      v-model:show=\"showUserAgreementModal\"\n      title=\"用户服务协议\"\n      :showCancelButton=\"false\"\n      confirmText=\"我知道了\"\n      confirmColor=\"#4CAF50\"\n      @confirm=\"showUserAgreementModal = false\"\n    >\n      <view class=\"modal-content\">\n        <text class=\"modal-text\">{{ userAgreementContent }}</text>\n      </view>\n    </u-modal>\n\n    <!-- 隐私政策模态框 -->\n    <u-modal\n      v-model:show=\"showPrivacyPolicyModal\"\n      title=\"隐私政策\"\n      :showCancelButton=\"false\"\n      confirmText=\"我知道了\"\n      confirmColor=\"#4CAF50\"\n      @confirm=\"showPrivacyPolicyModal = false\"\n    >\n      <view class=\"modal-content\">\n        <text class=\"modal-text\">{{ privacyPolicyContent }}</text>\n      </view>\n    </u-modal>\n\n    <!-- Toast 消息提示 -->\n    <u-toast ref=\"toastRef\" />\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed } from 'vue'\nimport { storeToRefs } from 'pinia'\nimport { useUserStore } from '@/src/stores/modules/user'\nimport { wxLogin } from '@/src/api/modules/user'\nimport type { LoginParams, UserInfo, WxLoginResponse } from '@/src/types/api'\n\n// ==================== Interfaces ====================\ninterface ToastInstance {\n  show: (options: {\n    title: string\n    type?: 'success' | 'error' | 'warning' | 'info'\n    duration?: number\n  }) => void\n}\n\n// ==================== Store ====================\nconst userStore = useUserStore()\nconst { profile } = storeToRefs(userStore)\nconst { setProfile } = userStore\n\n// ==================== 响应式数据 ====================\n/** 是否同意用户协议 */\nconst agreedToTerms = ref<boolean>(false)\n/** 登录加载状态 */\nconst isLoading = ref<boolean>(false)\n/** 显示用户协议模态框 */\nconst showUserAgreementModal = ref<boolean>(false)\n/** 显示隐私政策模态框 */\nconst showPrivacyPolicyModal = ref<boolean>(false)\n\n// ==================== Toast 引用 ====================\nconst toastRef = ref<ToastInstance | null>(null)\n\n// ==================== 设计系统 ====================\n/** 登录按钮样式 - 医疗健康主题 */\nconst loginButtonStyle = computed(() => ({\n  width: '100%',\n  height: '96rpx',\n  background: agreedToTerms.value\n    ? 'linear-gradient(135deg, #4CAF50, #2196F3)'\n    : '#e0e0e0',\n  color: '#ffffff',\n  border: 'none',\n  borderRadius: '48rpx',\n  boxShadow: agreedToTerms.value\n    ? '0 8rpx 24rpx rgba(76, 175, 80, 0.3), 0 4rpx 12rpx rgba(33, 150, 243, 0.2)'\n    : '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',\n  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n  opacity: agreedToTerms.value ? 1 : 0.6,\n  transform: agreedToTerms.value ? 'translateY(0)' : 'translateY(2rpx)',\n}))\n\n/** 用户协议内容 */\nconst userAgreementContent = computed(() => `1. 本系统为疾控机构专用的任职资格考试平台\n2. 用户需提供真实有效的个人信息\n3. 考试过程中需遵守相关规定\n4. 系统会记录用户的学习和考试行为\n5. 用户信息将严格保密，仅用于考试管理\n\n详细协议内容请联系管理员获取。`)\n\n/** 隐私政策内容 */\nconst privacyPolicyContent = computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录\n2. 信息用途：身份验证、考试管理、成绩统计\n3. 信息保护：采用加密存储，严格权限控制\n4. 信息共享：仅与相关机构共享必要信息\n5. 用户权利：可查看、修改个人信息\n\n详细政策内容请联系管理员获取。`)\n\n// ==================== 事件处理 ====================\n\n\n/**\n * 显示用户服务协议\n */\nfunction showUserAgreement(): void {\n  showUserAgreementModal.value = true\n}\n\n/**\n * 显示隐私政策\n */\nfunction showPrivacyPolicy(): void {\n  showPrivacyPolicyModal.value = true\n}\n\n/**\n * 显示Toast消息\n * @param title 消息标题\n * @param type 消息类型\n */\nfunction showToast(title: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {\n  if (toastRef.value) {\n    toastRef.value.show({\n      title,\n      type,\n      duration: type === 'success' ? 1500 : 2000,\n    })\n  }\n}\n\n/**\n * 微信授权登录\n */\nasync function handleWxLogin(): Promise<void> {\n  // 检查协议同意状态\n  if (!agreedToTerms.value) {\n    showToast('请先同意用户协议', 'warning')\n    return\n  }\n\n  isLoading.value = true\n\n  try {\n    // 调用微信登录获取code\n    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {\n      uni.login({\n        provider: 'weixin',\n        success: resolve,\n        fail: reject,\n      })\n    })\n\n    // 构造登录参数\n    const loginParams: LoginParams = {\n      code: loginResult.code,\n    }\n\n    // 调用后端登录接口\n    const loginResponse = await wxLogin(loginParams)\n\n    // 构造用户信息对象\n    const userInfo: UserInfo = {\n      id: '', // 临时ID，后续通过获取用户信息接口补充\n      openid: '',\n      nickname: '',\n      avatar: '',\n      status: loginResponse.userStatus,\n      token: loginResponse.token\n    }\n\n    // 保存用户信息到Store\n    setProfile(userInfo)\n\n    // 登录成功提示\n    showToast('登录成功', 'success')\n\n    // 根据用户状态进行页面跳转\n    setTimeout(() => {\n      navigateByUserStatus(loginResponse.userStatus)\n    }, 1500)\n\n  } catch (error) {\n    uni.__f__('error','at pages/login/login.vue:271','微信登录失败:', error)\n    showToast('登录失败，请重试', 'error')\n  } finally {\n    isLoading.value = false\n  }\n}\n\n/**\n * 根据用户状态进行页面跳转\n * @param status 用户状态\n */\nfunction navigateByUserStatus(status: UserInfo['status']): void {\n  switch (status) {\n    case 'approved':\n      // 已审核通过的正式用户，跳转到信息中心\n      uni.reLaunch({ url: '/pages/info/info' })\n      break\n    case 'pending_review':\n      // 待审核用户，跳转到个人中心查看审核状态\n      uni.reLaunch({ url: '/pages/profile/profile' })\n      break\n    case 'rejected':\n      // 审核未通过用户，跳转到个人中心修改资料\n      uni.reLaunch({ url: '/pages/profile/profile' })\n      break\n    case 'new':\n    default:\n      // 未提交资料的新用户，跳转到注册页面\n      uni.navigateTo({ url: '/pages/register/register' })\n      break\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/*\n  疾控考试系统登录页面样式\n  设计主题：医疗健康风格 - 浅蓝+白色，绿蓝渐变\n  复选框修复版本：2025-06-17T23:48:55\n*/\n\n/* ==================== 页面基础设置 ==================== */\n.login-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 50%, #e8f5e8 100%);\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n/* ==================== 背景装饰 ==================== */\n.background-decoration {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.decoration-circle {\n  position: absolute;\n  border-radius: 50%;\n  background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));\n\n  &--1 {\n    width: 320rpx;\n    height: 320rpx;\n    top: -160rpx;\n    right: -160rpx;\n    background: linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(33, 150, 243, 0.08));\n  }\n\n  &--2 {\n    width: 240rpx;\n    height: 240rpx;\n    bottom: 20%;\n    left: -120rpx;\n    background: linear-gradient(45deg, rgba(33, 150, 243, 0.06), rgba(76, 175, 80, 0.06));\n  }\n\n  &--3 {\n    width: 180rpx;\n    height: 180rpx;\n    top: 30%;\n    left: 20%;\n    background: linear-gradient(45deg, rgba(76, 175, 80, 0.04), rgba(33, 150, 243, 0.04));\n  }\n}\n\n/* ==================== 主容器 ==================== */\n.main-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  z-index: 10;\n  padding: 80rpx 60rpx 60rpx;\n}\n\n/* ==================== 品牌区域 ==================== */\n.brand-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 120rpx;\n}\n\n.logo-wrapper {\n  margin-bottom: 40rpx;\n}\n\n.logo-background {\n  width: 120rpx;\n  height: 120rpx;\n  background: linear-gradient(135deg, #4CAF50, #2196F3);\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow:\n    0 8rpx 24rpx rgba(76, 175, 80, 0.2),\n    0 4rpx 12rpx rgba(33, 150, 243, 0.15);\n}\n\n.brand-text {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.main-title {\n  font-size: 40rpx;\n  color: #2c5aa0;\n  font-weight: 600;\n  line-height: 1.2;\n}\n\n.sub-title {\n  font-size: 28rpx;\n  color: #5a7ba8;\n  line-height: 1.4;\n}\n\n/* ==================== 登录区域 ==================== */\n.login-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  /* 确保按钮位于60-70%高度 */\n  margin-top: auto;\n  margin-bottom: auto;\n  transform: translateY(-10%);\n}\n\n.login-button-wrapper {\n  width: 100%;\n  margin-bottom: 48rpx;\n}\n\n\n\n/* ==================== 协议区域 ==================== */\n.agreement-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.checkbox-container {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.agreement-links {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.agreement-text {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.4;\n}\n\n.agreement-link {\n  font-size: 26rpx;\n  color: #4CAF50;\n  line-height: 1.4;\n  text-decoration: underline;\n  cursor: pointer;\n}\n\n/* ==================== 模态框内容 ==================== */\n.modal-content {\n  padding: 32rpx 24rpx;\n  max-height: 600rpx;\n  overflow-y: auto;\n}\n\n.modal-text {\n  font-size: 28rpx;\n  color: #333333;\n  line-height: 1.6;\n  white-space: pre-line;\n}\n\n/* ==================== uview-plus组件样式定制 ==================== */\n/* 复选框样式优化 - 清晰的视觉反馈 */\n:deep(.u-checkbox__icon-wrap) {\n  border: 2rpx solid #e0e0e0 !important;\n  transition: all 0.3s ease !important;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;\n}\n\n:deep(.u-checkbox--checked .u-checkbox__icon-wrap) {\n  box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.3) !important;\n}\n\n/* 协议链接点击效果 */\n:deep(.u-text) {\n  cursor: pointer;\n  transition: opacity 0.2s ease;\n\n  &:active {\n    opacity: 0.7;\n  }\n}\n\n/* ==================== 响应式适配 ==================== */\n@media screen and (max-width: 750rpx) {\n  .main-container {\n    padding: 60rpx 40rpx 40rpx;\n  }\n\n  .logo-background {\n    width: 100rpx;\n    height: 100rpx;\n  }\n\n  .brand-section {\n    margin-bottom: 100rpx;\n  }\n}\n\n@media screen and (max-width: 600rpx) {\n  .main-container {\n    padding: 40rpx 32rpx 32rpx;\n  }\n\n  .logo-background {\n    width: 80rpx;\n    height: 80rpx;\n  }\n\n  .brand-section {\n    margin-bottom: 80rpx;\n  }\n\n  .login-section {\n    transform: translateY(-5%);\n  }\n}\n</style>\n\n<!-- 新增：全局page高度修正，确保小程序下全屏 -->\n<style>\npage {\n  height: 100%;\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "ref", "computed", "uni", "wxL<PERSON>in"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAoIA,UAAM,YAAYA,wBAAAA;AACEC,kBAAAA,YAAY,SAAS;AACnC,UAAA,EAAE,WAAe,IAAA;AAIjB,UAAA,gBAAgBC,kBAAa,KAAK;AAElC,UAAA,YAAYA,kBAAa,KAAK;AAE9B,UAAA,yBAAyBA,kBAAa,KAAK;AAE3C,UAAA,yBAAyBA,kBAAa,KAAK;AAG3C,UAAA,WAAWA,kBAA0B,IAAI;AAIzC,UAAA,mBAAmBC,cAAAA,SAAS,OAAO;AAAA,MACvC,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY,cAAc,QACtB,8CACA;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,WAAW,cAAc,QACrB,8EACA;AAAA,MACJ,YAAY;AAAA,MACZ,SAAS,cAAc,QAAQ,IAAI;AAAA,MACnC,WAAW,cAAc,QAAQ,kBAAkB;AAAA,IACnD,EAAA;AAGI,UAAA,uBAAuBA,uBAAS,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAM5B;AAGV,UAAA,uBAAuBA,uBAAS,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAM5B;AAQhB,aAAS,oBAA0B;AACjC,6BAAuB,QAAQ;AAAA,IACjC;AAKA,aAAS,oBAA0B;AACjC,6BAAuB,QAAQ;AAAA,IACjC;AAOS,aAAA,UAAU,OAAe,OAAiD,QAAc;AAC/F,UAAI,SAAS,OAAO;AAClB,iBAAS,MAAM,KAAK;AAAA,UAClB;AAAA,UACA;AAAA,UACA,UAAU,SAAS,YAAY,OAAO;AAAA,QAAA,CACvC;AAAA,MACH;AAAA,IACF;AAKA,mBAAe,gBAA+B;AAExC,UAAA,CAAC,cAAc,OAAO;AACxB,kBAAU,YAAY,SAAS;AAC/B;AAAA,MACF;AAEA,gBAAU,QAAQ;AAEd,UAAA;AAEF,cAAM,cAAc,MAAM,IAAI,QAAyB,CAAC,SAAS,WAAW;AAC1EC,wBAAAA,MAAI,MAAM;AAAA,YACR,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,UAAA,CACP;AAAA,QAAA,CACF;AAGD,cAAM,cAA2B;AAAA,UAC/B,MAAM,YAAY;AAAA,QAAA;AAId,cAAA,gBAAgB,MAAMC,6BAAQ,WAAW;AAG/C,cAAM,WAAqB;AAAA,UACzB,IAAI;AAAA;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,QAAQ,cAAc;AAAA,UACtB,OAAO,cAAc;AAAA,QAAA;AAIvB,mBAAW,QAAQ;AAGnB,kBAAU,QAAQ,SAAS;AAG3B,mBAAW,MAAM;AACf,+BAAqB,cAAc,UAAU;AAAA,WAC5C,IAAI;AAAA,eAEA,OAAO;AACdD,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,WAAW,KAAK;AACjE,kBAAU,YAAY,OAAO;AAAA,MAAA,UAC7B;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AAMA,aAAS,qBAAqB,QAAkC;AAC9D,cAAQ,QAAQ;AAAA,QACd,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,mBAAoB,CAAA;AACxC;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAC9C;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAC9C;AAAA,QACF,KAAK;AAAA,QACL;AAEEA,wBAAAA,MAAI,WAAW,EAAE,KAAK,2BAA4B,CAAA;AAClD;AAAA,MACJ;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5SA,GAAG,WAAW,eAAe;"}