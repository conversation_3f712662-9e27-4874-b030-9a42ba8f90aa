"use strict";
const src_utils_request = require("../../utils/request.js");
function wxLogin(params) {
  return src_utils_request.http.post("/auth/wechat-login", params, {
    custom: {
      skipResponseInterceptor: true
    }
  });
}
function getInstitutions() {
  return src_utils_request.http.get("/institutions");
}
function getPositions() {
  return src_utils_request.http.get("/positions");
}
function submitUserInfo(params) {
  return src_utils_request.http.post("/profile", params);
}
exports.getInstitutions = getInstitutions;
exports.getPositions = getPositions;
exports.submitUserInfo = submitUserInfo;
exports.wxLogin = wxLogin;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/api/modules/user.js.map
