{"version": 3, "file": "request.js", "sources": ["src/utils/request.ts"], "sourcesContent": ["/**\n * HTTP请求封装\n * 基于luch-request的统一请求封装\n */\nimport Request from 'luch-request';\nimport { useUserStore } from '../stores/modules/user';\nimport type { ApiResponse } from '../types/api';\n\n// 创建请求实例\nconst http = new Request({\n  baseURL: 'http://127.0.0.1:3000/api/v1', // TODO: 替换为实际的API地址\n  timeout: 10000,\n  header: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(\n  (config) => {\n    // 显示加载提示\n    uni.showLoading({\n      title: '加载中...',\n      mask: true,\n    });\n\n    // 添加token\n    const userStore = useUserStore();\n    if (userStore.token) {\n      config.header.Authorization = `Bearer ${userStore.token}`;\n    }\n\n    return config;\n  },\n  (error) => {\n    uni.hideLoading();\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nhttp.interceptors.response.use(\n  (response) => {\n    uni.hideLoading();\n\n    // 检查是否跳过响应拦截器处理\n    if (response.config?.custom?.skipResponseInterceptor) {\n      return Promise.resolve(response.data);\n    }\n\n    // 统一以 HTTP statusCode 为判断依据\n    const { statusCode, data } = response as {\n      statusCode: number;\n      data: any;\n    };\n\n    // 2xx/3xx → 视为成功，直接透出 data\n    if (statusCode >= 200 && statusCode < 400) {\n      return Promise.resolve(data);\n    }\n\n    // Token 失效\n    if (statusCode === 401) {\n      const userStore = useUserStore();\n      userStore.clearProfile();\n      \n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none',\n        duration: 2000,\n      });\n      \n      // 跳转到登录页\n      setTimeout(() => {\n        uni.reLaunch({\n          url: '/pages/login/login',\n        });\n      }, 2000);\n      \n      return Promise.reject(data);\n    }\n\n    // 其他错误（4xx/5xx）\n    uni.showToast({\n      title: data?.message || '请求失败',\n      icon: 'none',\n      duration: 2000,\n    });\n    \n    return Promise.reject(data);\n  },\n  (error) => {\n    uni.hideLoading();\n    \n    console.error('HTTP请求错误:', error);\n    \n    // 网络错误处理\n    let errorMessage = '网络错误，请稍后重试';\n    \n    if (error.statusCode) {\n      switch (error.statusCode) {\n        case 404:\n          errorMessage = '请求的资源不存在';\n          break;\n        case 500:\n          errorMessage = '服务器内部错误';\n          break;\n        case 502:\n          errorMessage = '网关错误';\n          break;\n        case 503:\n          errorMessage = '服务不可用';\n          break;\n        case 504:\n          errorMessage = '网关超时';\n          break;\n        default:\n          errorMessage = `请求失败 (${error.statusCode})`;\n      }\n    }\n    \n    uni.showToast({\n      title: errorMessage,\n      icon: 'none',\n      duration: 2000,\n    });\n    \n    return Promise.reject(error);\n  }\n);\n\nexport default http;\n"], "names": ["Request", "uni", "useUserStore"], "mappings": ";;;AASM,MAAA,OAAO,IAAIA,cAAAA,QAAQ;AAAA,EACvB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,IACN,gBAAgB;AAAA,EAClB;AACF,CAAC;AAGD,KAAK,aAAa,QAAQ;AAAA,EACxB,CAAC,WAAW;AAEVC,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IAAA,CACP;AAGD,UAAM,YAAYC,wBAAAA;AAClB,QAAI,UAAU,OAAO;AACnB,aAAO,OAAO,gBAAgB,UAAU,UAAU,KAAK;AAAA,IACzD;AAEO,WAAA;AAAA,EACT;AAAA,EACA,CAAC,UAAU;AACTD,kBAAA,MAAI,YAAY;AACT,WAAA,QAAQ,OAAO,KAAK;AAAA,EAC7B;AACF;AAGA,KAAK,aAAa,SAAS;AAAA,EACzB,CAAC,aAAa;;AACZA,kBAAA,MAAI,YAAY;AAGZ,SAAA,oBAAS,WAAT,mBAAiB,WAAjB,mBAAyB,yBAAyB;AAC7C,aAAA,QAAQ,QAAQ,SAAS,IAAI;AAAA,IACtC;AAGM,UAAA,EAAE,YAAY,KAAS,IAAA;AAMzB,QAAA,cAAc,OAAO,aAAa,KAAK;AAClC,aAAA,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AAGA,QAAI,eAAe,KAAK;AACtB,YAAM,YAAYC,wBAAAA;AAClB,gBAAU,aAAa;AAEvBD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MAAA,CACX;AAGD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA,QAAA,CACN;AAAA,SACA,GAAI;AAEA,aAAA,QAAQ,OAAO,IAAI;AAAA,IAC5B;AAGAA,kBAAAA,MAAI,UAAU;AAAA,MACZ,QAAO,6BAAM,YAAW;AAAA,MACxB,MAAM;AAAA,MACN,UAAU;AAAA,IAAA,CACX;AAEM,WAAA,QAAQ,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,CAAC,UAAU;AACTA,kBAAA,MAAI,YAAY;AAEhBA,kBAAA,MAAA,MAAA,SAAA,8BAAc,aAAa,KAAK;AAGhC,QAAI,eAAe;AAEnB,QAAI,MAAM,YAAY;AACpB,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF;AACiB,yBAAA,SAAS,MAAM,UAAU;AAAA,MAC5C;AAAA,IACF;AAEAA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAAA,CACX;AAEM,WAAA,QAAQ,OAAO,KAAK;AAAA,EAC7B;AACF;;"}