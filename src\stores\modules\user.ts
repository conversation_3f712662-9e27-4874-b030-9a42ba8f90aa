/**
 * 用户状态管理
 */
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type { UserInfo } from '../../types/api';

export const useUserStore = defineStore('user', () => {
  // State
  const profile = ref<UserInfo | null>(null);

  // Getters
  const isLoggedIn = computed(() => !!profile.value?.token);
  const token = computed(() => profile.value?.token || '');
  const userStatus = computed(() => profile.value?.status || 'new');
  const isApproved = computed(() => profile.value?.status === 'approved');
  const isPendingReview = computed(() => profile.value?.status === 'pending_review');
  const isRejected = computed(() => profile.value?.status === 'rejected');
  const isNew = computed(() => profile.value?.status === 'new');

  // Actions
  function setProfile(userInfo: UserInfo) {
    profile.value = userInfo;
    // 持久化存储
    uni.setStorageSync('user_profile', userInfo);
  }

  function updateProfile(updates: Partial<UserInfo>) {
    if (profile.value) {
      profile.value = { ...profile.value, ...updates };
      uni.setStorageSync('user_profile', profile.value);
    }
  }

  function clearProfile() {
    profile.value = null;
    uni.removeStorageSync('user_profile');
  }

  function initProfile() {
    try {
      const savedProfile = uni.getStorageSync('user_profile');
      if (savedProfile) {
        profile.value = savedProfile;
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error);
    }
  }

  return {
    // State
    profile,
    // Getters
    isLoggedIn,
    token,
    userStatus,
    isApproved,
    isPendingReview,
    isRejected,
    isNew,
    // Actions
    setProfile,
    updateProfile,
    clearProfile,
    initProfile,
  };
});
