<template>
  <view class="register-container">
    <!-- 页面头部提示 -->
    <view class="register-header">
      <text class="page-title">完善个人资料</text>
      <text class="page-desc">请填写真实有效的个人信息，以便机构审核</text>
    </view>

    <!-- 注册表单 -->
    <view class="register-form">
      <u-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        labelPosition="top"
        labelWidth="auto"
      >
        <!-- 真实姓名 -->
        <u-form-item label="真实姓名" prop="realName" required>
          <u-input
            v-model="formData.realName"
            placeholder="请输入真实姓名"
            clearable
            maxlength="20"
          />
        </u-form-item>

        <!-- 联系电话 -->
        <u-form-item label="联系电话" prop="phone" required>
          <u-input
            v-model="formData.phone"
            placeholder="请输入手机号码"
            type="number"
            clearable
            maxlength="11"
          />
        </u-form-item>

        <!-- 身份证号 -->
        <u-form-item label="身份证号码" prop="idCard" required>
          <u-input
            v-model="formData.idCard"
            placeholder="请输入身份证号码"
            clearable
            maxlength="18"
          />
        </u-form-item>

        <!-- 隶属机构 -->
        <u-form-item label="隶属机构" prop="institutionId" required>
          <u-input
            v-model="selectedInstitutionName"
            placeholder="请选择所属机构"
            readonly
            @click="selectInstitution"
          />
        </u-form-item>

        <!-- 职位 -->
        <u-form-item label="职位" prop="positionId" required>
          <u-input
            v-model="selectedPositionName"
            placeholder="请选择职位"
            readonly
            @click="selectPosition"
          />
        </u-form-item>

        <!-- 本人照片 -->
        <u-form-item label="本人照片" prop="avatar" required>
          <view class="avatar-upload">
            <view v-if="!formData.avatar" class="upload-placeholder" @click="uploadAvatar">
              <u-icon name="camera" size="60" color="#cccccc" />
              <text class="upload-text">点击上传本人正面照片</text>
              <text class="upload-desc">用于人脸识别验证</text>
            </view>
            <view v-else class="avatar-preview" @click="uploadAvatar">
              <image :src="formData.avatar" mode="aspectFill" class="avatar-image" />
              <view class="avatar-mask">
                <u-icon name="camera" size="40" color="#ffffff" />
              </view>
            </view>
          </view>
        </u-form-item>
      </u-form>
    </view>

    <!-- 操作按钮 -->
    <view class="register-actions">
      <u-button 
        type="primary"
        :loading="isSubmitting"
        loadingText="提交中..."
        @click="submitForm"
        customStyle="width: 100%; margin-bottom: 24rpx;"
      >
        提交审核
      </u-button>
      
      <u-button 
        type="info"
        plain
        @click="skipRegister"
        customStyle="width: 100%;"
      >
        跳过，先去学习
      </u-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/src/stores/modules/user';
import { submitUserInfo, getInstitutions, getPositions } from '@/src/api/modules/user';
import type { RegisterParams, Institution, Position } from '@/src/types/api';

// ==================== Store ====================
const userStore = useUserStore();
const { profile } = storeToRefs(userStore);
const { updateProfile } = userStore;

// ==================== 响应式数据 ====================
/** 表单引用 */
const formRef = ref();
/** 提交状态 */
const isSubmitting = ref<boolean>(false);

/** 机构数据 */
const institutions = ref<Institution[]>([]);
/** 职位数据 */
const positions = ref<Position[]>([]);

/** 表单数据 */
const formData = reactive<RegisterParams>({
  realName: '',
  phone: '',
  idCard: '',
  institutionId: '',
  positionId: '',
  avatar: '',
});

/** 选中的机构名称 */
const selectedInstitutionName = computed(() => {
  const institution = institutions.value.find(item => item.id === formData.institutionId);
  return institution?.name || '';
});

/** 选中的职位名称 */
const selectedPositionName = computed(() => {
  const position = positions.value.find(item => item.id === formData.positionId);
  return position?.name || '';
});

/** 表单验证规则 */
const formRules = reactive({
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2-20个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  idCard: [
    { required: true, message: '请输入身份证号码', trigger: 'blur' },
    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' },
  ],
  institutionId: [
    { required: true, message: '请选择隶属机构', trigger: 'change' },
  ],
  positionId: [
    { required: true, message: '请选择职位', trigger: 'change' },
  ],
  avatar: [
    { required: true, message: '请上传本人照片', trigger: 'change' },
  ],
});

// ==================== 生命周期 ====================
onMounted(() => {
  loadInitialData();
});

// ==================== 事件处理 ====================
/**
 * 加载初始数据
 */
async function loadInitialData(): Promise<void> {
  try {
    uni.showLoading({ title: '加载中...' });
    
    const [institutionsData, positionsData] = await Promise.all([
      getInstitutions(),
      getPositions(),
    ]);
    
    institutions.value = institutionsData;
    positions.value = positionsData;
    
    uni.hideLoading();
  } catch (error) {
    uni.hideLoading();
    console.error('加载机构和职位数据失败:', error);
    uni.showToast({
      title: '数据加载失败，请重试',
      icon: 'none',
    });
  }
}

/**
 * 选择机构
 */
function selectInstitution(): void {
  if (institutions.value.length === 0) {
    uni.showToast({
      title: '机构数据尚未加载',
      icon: 'none',
    });
    return;
  }
  
  uni.showActionSheet({
    itemList: institutions.value.map(item => item.name),
    success: (res) => {
      const selectedInstitution = institutions.value[res.tapIndex];
      formData.institutionId = selectedInstitution.id;
    },
  });
}

/**
 * 选择职位
 */
function selectPosition(): void {
  if (positions.value.length === 0) {
    uni.showToast({
      title: '职位数据尚未加载',
      icon: 'none',
    });
    return;
  }
  
  uni.showActionSheet({
    itemList: positions.value.map(item => item.name),
    success: (res) => {
      const selectedPosition = positions.value[res.tapIndex];
      formData.positionId = selectedPosition.id;
    },
  });
}

/**
 * 上传头像
 */
function uploadAvatar(): void {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: async (res) => {
      try {
        uni.showLoading({ title: '上传中...' });
        
        // 这里应该调用上传API，暂时直接使用本地路径
        formData.avatar = res.tempFilePaths[0];
        
        uni.hideLoading();
        uni.showToast({
          title: '上传成功',
          icon: 'success',
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none',
        });
      }
    },
  });
}

/**
 * 提交表单
 */
async function submitForm(): Promise<void> {
  try {
    // 表单验证
    await formRef.value.validate();
    
    isSubmitting.value = true;
    
    // 提交注册信息
    await submitUserInfo(formData);
    
    // 更新用户状态为待审核
    updateProfile({ status: 'pending_review' });
    
    uni.showToast({
      title: '提交成功，请等待审核',
      icon: 'success',
      duration: 2000,
    });
    
    // 跳转到个人中心
    setTimeout(() => {
      uni.reLaunch({ url: '/pages/profile/profile' });
    }, 2000);
    
  } catch (error) {
    console.error('提交失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    });
  } finally {
    isSubmitting.value = false;
  }
}

/**
 * 跳过注册
 */
function skipRegister(): void {
  uni.showModal({
    title: '确认跳过',
    content: '跳过后您将无法参加考试，只能进行学习练习，确认跳过吗？',
    success: (res) => {
      if (res.confirm) {
        // 跳转到学习中心
        uni.reLaunch({ url: '/pages/study/study' });
      }
    },
  });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

/* ==================== 主容器 ==================== */
.register-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: $spacing-lg;
}

/* ==================== 头部区域 ==================== */
.register-header {
  text-align: center;
  margin-bottom: $spacing-xl;
}

.page-title {
  display: block;
  font-size: 36rpx;
  color: #1976d2;
  font-weight: bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #757575;
}

/* ==================== 表单区域 ==================== */
.register-form {
  margin-bottom: $spacing-xl;
}

/* ==================== 头像上传 ==================== */
.avatar-upload {
  width: 200rpx;
  height: 200rpx;
  border-radius: $border-radius-medium;
  overflow: hidden;
  border: 2rpx dashed $divider-color;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: $surface-color;
}

.upload-text {
  font-size: 26rpx;
  color: #999999;
  margin-top: 16rpx;
}

.upload-desc {
  font-size: 22rpx;
  color: #cccccc;
  margin-top: 8rpx;
}

.avatar-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-preview:active .avatar-mask {
  opacity: 1;
}

/* ==================== 操作按钮 ==================== */
.register-actions {
  padding: $spacing-md 0;
}
</style>
