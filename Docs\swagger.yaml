openapi: 3.0.3
info:
  title: 疾控医护任职资格考试系统 API
  description: |
    由认知API架构师根据产品需求文档（2025年6月9日版）生成的API技术规范。
    该系统旨在为疾控机构医护人员提供一站式任职资格考试及学习辅助服务。
  version: 1.0.0
servers:
  - url: https://api.yourdomain.com/api/v1
    description: 生产环境服务器
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器

tags:
  - name: 认证与用户 (Auth & User)
    description: 涉及用户登录、注册、状态管理和资料提交。
  - name: 信息中心 (Information)
    description: 公告、政策、通知等资讯的获取。
  - name: 学习中心 (Learning)
    description: 题库练习相关功能。
  - name: 考试中心 (Exam)
    description: 线上考试、线下报名及历史记录。
  - name: 个人中心 (Profile)
    description: 个人信息、证书管理等。
  - name: 通用 (Common)
    description: 反馈、关于我们等通用功能。

paths:
  # 认证与用户
  /auth/wechat-login:
    post:
      tags:
        - 认证与用户 (Auth & User)
      summary: 微信授权登录
      description: 用户通过微信授权后，使用code换取系统token和用户状态。
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: 通过 wx.login() 获取的临时登录凭证。
                  example: "0a3j3J1c0c..."
              required:
                - code
      responses:
        '200':
          description: 登录成功或用户已存在。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          token:
                            type: string
                            description: 用于后续请求的JWT。
                          userStatus:
                            $ref: '#/components/schemas/UserStatus'
        '400':
          $ref: '#/components/responses/BadRequest'

  /institutions:
    get:
      tags:
        - 认证与用户 (Auth & User)
      summary: 获取机构列表
      description: 获取所有可选的机构列表，用于用户注册时选择所属机构。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取机构列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Institution'
                example:
                  code: 200
                  message: "OK"
                  data:
                    - id: "11111111-1111-1111-1111-111111111111"
                      name: "市疾控中心"
                    - id: "22222222-2222-2222-2222-222222222222"
                      name: "区疾控中心"
        '401':
          $ref: '#/components/responses/Unauthorized'

  /positions:
    get:
      tags:
        - 认证与用户 (Auth & User)
      summary: 获取职位列表
      description: 获取所有可选的职位列表，用于用户注册时选择职位。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取职位列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Position'
                example:
                  code: 200
                  message: "OK"
                  data:
                    - id: "33333333-3333-3333-3333-333333333333"
                      name: "预防接种医生"
                    - id: "44444444-4444-4444-4444-444444444444"
                      name: "检验技师"
        '401':
          $ref: '#/components/responses/Unauthorized'

  /profile:
    post:
      tags:
        - 认证与用户 (Auth & User)
      summary: 提交个人资料 (新用户注册)
      description: 新用户或未提交资料的用户提交个人从业信息以供审核。照片需要通过文件上传。
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProfileSubmissionRequest'
      responses:
        '201':
          description: 资料提交成功，等待审核。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
    put:
      tags:
        - 认证与用户 (Auth & User)
      summary: 修改个人资料 (审核不通过后)
      description: 当用户资料审核被驳回后，可调用此接口修改并重新提交。
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProfileSubmissionRequest'
      responses:
        '200':
          description: 资料修改并重新提交成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /profile/me:
    get:
      tags:
        - 个人中心 (Profile)
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细个人信息。后端应根据用户状态返回不同数据，并对敏感信息进行脱敏。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取用户信息。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  # 信息中心
  /articles:
    get:
      tags:
        - 信息中心 (Information)
      summary: 获取资讯列表
      description: 获取公告、政策法规、重要通知的列表。通过type参数进行筛选。
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ArticleType'
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: 成功获取资讯列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/Article'
                          total:
                            type: integer
                          page:
                            type: integer
                          pageSize:
                            type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'

  /articles/{articleId}:
    get:
      tags:
        - 信息中心 (Information)
      summary: 获取资讯详情
      description: 根据ID获取单条资讯的详细内容。
      security:
        - bearerAuth: []
      parameters:
        - name: articleId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取资讯详情。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Article'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  # 学习中心
  /learning/question-bank/categories:
    get:
      tags:
        - 学习中心 (Learning)
      summary: 获取题库分类列表
      description: 获取所有可供练习的题库分类。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取分类列表。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/QuestionBankCategory'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /learning/question-bank/practice-questions:
    get:
      tags:
        - 学习中心 (Learning)
      summary: 获取一组练习题
      description: 根据分类ID随机获取一组（例如10道）练习题。后端需要处理用户的练习次数限制。
      security:
        - bearerAuth: []
      parameters:
        - name: categoryId
          in: query
          required: true
          schema:
            type: string
            format: uuid
        - name: count
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功获取练习题。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Question'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 练习次数已用完。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ErrorResponse'

  # 考试中心
  /exams/current:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取本期待考列表
      description: 获取当前用户需要参加的线上或线下考试列表。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取待考列表。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Exam'
        '401':
          $ref: '#/components/responses/Unauthorized'
  
  /exams/history:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取历史考试记录
      description: 获取用户已完成的所有考试记录，支持分页。
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: 成功获取历史记录列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ExamAttempt'
                          total:
                            type: integer
                          page:
                            type: integer
                          pageSize:
                            type: integer
        '401':
          $ref: '#/components/responses/Unauthorized'
          
  /exams/online/{examId}/rules:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取线上考试考前须知
      description: 在正式开始考试前，获取考前阅读的规则和须知。
      security:
        - bearerAuth: []
      parameters:
        - name: examId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取考前须知。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          rules:
                            type: string
                            description: 考前须知内容，支持富文本。
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /exams/online/{examId}/attempts:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 开始线上考试尝试 (含人脸识别)
      description: |
        这是一个关键的原子操作。用户调用此接口开始一次新的考试尝试。
        请求体中必须包含用于人脸识别的现场照片。
        后端完成人脸比对，成功后才返回唯一的`attemptId`和本次考试的题目。
        失败则返回错误，不允许进入答题。
      security:
        - bearerAuth: []
      parameters:
        - name: examId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                faceImage:
                  type: string
                  format: binary
                  description: 用户在考前通过摄像头现场拍摄的照片。
              required:
                - faceImage
      responses:
        '201':
          description: 人脸识别成功，考试开始。返回考题和attemptId。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          attemptId:
                            type: string
                            format: uuid
                            description: 本次考试尝试的唯一ID。
                          questions:
                            type: array
                            items:
                              $ref: '#/components/schemas/Question'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 人脸识别失败或已无重试机会。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ErrorResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  /exams/online/attempts/{attemptId}/answers:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 提交线上考试答案
      description: 提交指定考试尝试的所有答案。
      security:
        - bearerAuth: []
      parameters:
        - name: attemptId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                answers:
                  type: array
                  items:
                    type: object
                    properties:
                      questionId:
                        type: string
                        format: uuid
                      answer:
                        type: string # 多选题答案用逗号分隔等方式约定
              required:
                - answers
      responses:
        '200':
          description: 答卷提交成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /exams/online/attempts/{attemptId}/anticheat-logs:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 上报防作弊日志
      description: 考试过程中，前端监听到切屏等行为时，调用此接口上报。
      security:
        - bearerAuth: []
      parameters:
        - name: attemptId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                eventType:
                  type: string
                  enum: [screen_switch, face_capture]
                eventData:
                  type: object
                  description: 包含事件详情的JSON对象，如切屏时长、抓拍照片URL等。
      responses:
        '204':
          description: 日志记录成功。
        '401':
          $ref: '#/components/responses/Unauthorized'

  /exams/offline/{examId}/schedules:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取线下考试的考场和场次
      description: 查看指定线下考试的所有可用考场及各考场下的时间安排和名额。
      security:
        - bearerAuth: []
      parameters:
        - name: examId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取考场和场次信息。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/OfflineVenueSchedule'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /exams/offline/bookings:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 报名线下考试场次
      description: 为当前用户预约一个特定的线下考试场次。
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                scheduleId:
                  type: string
                  format: uuid
                  description: 考场时间安排的唯一ID。
              required:
                - scheduleId
      responses:
        '201':
          description: 报名成功。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OfflineBooking'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 报名失败（如名额已满或不符合条件）。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /exams/offline/bookings/{bookingId}:
    delete:
      tags:
        - 考试中心 (Exam)
      summary: 取消线下考试报名
      description: 取消一个已有的线下考试预约。
      security:
        - bearerAuth: []
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: 成功取消报名。
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 取消失败（如已过取消时限）。

  # 个人中心
  /profile/me/certificates:
    get:
      tags:
        - 个人中心 (Profile)
      summary: 获取我的证书
      description: 获取当前用户的所有证书信息，包括当前有效、审批中和历史证书。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取证书信息。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          currentCertificate:
                            $ref: '#/components/schemas/Certificate'
                          pendingCertificate:
                            $ref: '#/components/schemas/Certificate'
                          historyCertificates:
                            type: array
                            items:
                              $ref: '#/components/schemas/Certificate'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # 通用
  /feedback:
    post:
      tags:
        - 通用 (Common)
      summary: 提交投诉与建议
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: 反馈内容。
              required:
                - content
      responses:
        '201':
          description: 提交成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /app/info:
    get:
      tags:
        - 通用 (Common)
      summary: 获取关于我们的信息
      responses:
        '200':
          description: 成功获取应用信息。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          appName:
                            type: string
                          version:
                            type: string
                          description:
                            type: string
                          copyright:
                            type: string

components:
  schemas:
    # --- Generic API Response Wrapper ---
    ApiResponse:
      type: object
      description: 统一API响应包裹结构
      properties:
        code:
          type: integer
          description: 业务状态码，200表示成功
          example: 200
        message:
          type: string
          description: 响应消息
          example: "OK"
        data:
          description: 响应数据，可以是任意类型
      required:
        - code
        - message

    # --- Enums & Basic Types ---
    UserStatus:
      type: string
      enum: [new, pending_review, approved, rejected]
      description: |
        用户状态:
        - `new`: 新微信用户，未提交资料
        - `pending_review`: 已提交资料，待审核
        - `approved`: 审核通过，正式用户
        - `rejected`: 审核不通过
    ArticleType:
      type: string
      enum: [announcement, policy, notice]
      description: |
        资讯类型:
        - `announcement`: 公告
        - `policy`: 政策法规
        - `notice`: 重要通知
    ExamType:
      type: string
      enum: [online, offline]
    QuestionType:
      type: string
      enum: [single_choice, multiple_choice, judgment, essay]

    # --- Request Bodies ---
    ProfileSubmissionRequest:
      type: object
      properties:
        name:
          type: string
          description: 真实姓名
          example: 张三
        phone:
          type: string
          description: 手机号码
          example: '13800138000'
        idCardNumber:
          type: string
          description: 身份证号码
          example: '******************'
        photo:
          type: string
          format: binary
          description: 本人近期正面免冠证件照或清晰的生活照
        institutionId:
          type: string
          format: uuid
          description: 隶属机构ID
        positionId:
          type: string
          format: uuid
          description: 职位ID
      required:
        - name
        - phone
        - idCardNumber
        - photo
        - institutionId
        - positionId

    # --- Main Resources ---
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        wechatNickname:
          type: string
        wechatAvatarUrl:
          type: string
          format: uri
        status:
          $ref: '#/components/schemas/UserStatus'
        name:
          type: string
          description: 真实姓名 (审核通过后返回)
        phone:
          type: string
          description: 手机号 (后端返回时应脱敏，如 '138****8000')
        idCardNumber:
          type: string
          description: 身份证号 (后端返回时应脱敏，如 '4401**********0001')
        photoUrl:
          type: string
          format: uri
          description: 注册时提交的照片URL
        institutionName:
          type: string
          description: 隶属机构名称
        positionName:
          type: string
          description: 职位名称
        certificateExpiryDate:
          type: string
          format: date
          description: 当前有效证书的到期日
        createdAt:
          type: string
          format: date-time

    Institution:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 机构唯一标识
        name:
          type: string
          description: 机构名称
      required:
        - id
        - name

    Position:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 职位唯一标识
        name:
          type: string
          description: 职位名称
      required:
        - id
        - name

    Article:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        content:
          type: string
          description: 资讯正文，支持富文本HTML
        type:
          $ref: '#/components/schemas/ArticleType'
        isPinned:
          type: boolean
          description: 是否置顶
        createdAt:
          type: string
          format: date-time
    
    QuestionBankCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        totalQuestions:
          type: integer
    
    Question:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          $ref: '#/components/schemas/QuestionType'
        stem:
          type: string
          description: 题干
        options:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                example: A
              value:
                type: string
                example: '选项内容'
        correctAnswer:
          type: string
          description: 正确答案。多选可用逗号分隔的key，如'A,C'。问答题为空。
        explanation:
          type: string
          description: 答案解析。

    Exam:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        type:
          $ref: '#/components/schemas/ExamType'
        status:
          type: string
          description: 用户视角下的考试状态（未报名/已报名/未参与/已通过等）
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        canRetry:
          type: boolean
          description: 是否允许重考

    ExamAttempt:
      type: object
      properties:
        id:
          type: string
          format: uuid
        examName:
          type: string
        examType:
          $ref: '#/components/schemas/ExamType'
        completedAt:
          type: string
          format: date-time
        score:
          type: number
          format: float
        status:
          type: string
          enum: [passed, failed, pending_grading]
          description: 成绩状态

    OfflineVenueSchedule:
      type: object
      properties:
        venueId:
          type: string
          format: uuid
        venueName:
          type: string
        schedules:
          type: array
          items:
            type: object
            properties:
              scheduleId:
                type: string
                format: uuid
              startTime:
                type: string
                format: date-time
              endTime:
                type: string
                format: date-time
              totalSlots:
                type: integer
              availableSlots:
                type: integer
      
    OfflineBooking:
      type: object
      properties:
        bookingId:
          type: string
          format: uuid
        examName:
          type: string
        venueName:
          type: string
        startTime:
          type: string
          format: date-time

    Certificate:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: '接种门诊上岗资格证'
        status:
          type: string
          enum: [pending_approval, valid, expired, revoked]
        imageUrl:
          type: string
          format: uri
        issueDate:
          type: string
          format: date
        expiryDate:
          type: string
          format: date

    # --- Utility Schemas ---
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
        message:
          type: string
          description: 错误信息描述
      required:
        - code
        - message

  parameters:
    Page:
      name: page
      in: query
      description: 页码，从1开始
      schema:
        type: integer
        default: 1
    PageSize:
      name: pageSize
      in: query
      description: 每页数量
      schema:
        type: integer
        default: 10

  responses:
    BadRequest:
      description: 无效的请求参数。
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: 未授权，Token无效或过期。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: 请求的资源不存在。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 在请求头的 Authorization 字段中添加 "Bearer {token}"。