{"version": 3, "file": "profile.js", "sources": ["pages/profile/profile.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9wcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"profile-container\">\n    <!-- 用户信息头部 -->\n    <view class=\"profile-header\">\n      <image class=\"avatar\" :src=\"userStore.profile?.avatar || '/static/default-avatar.png'\" />\n      <view class=\"user-info\">\n        <text class=\"nickname\">{{ userStore.profile?.nickname || '未设置昵称' }}</text>\n        <view class=\"status-badge\" :class=\"userStore.userStatus\">\n          {{ getStatusText() }}\n        </view>\n      </view>\n    </view>\n    \n    <!-- 功能菜单 -->\n    <view class=\"menu-section\">\n      <!-- 个人信息相关 -->\n      <view class=\"menu-group\">\n        <view class=\"menu-item\" @tap=\"goToPersonalInfo\">\n          <view class=\"menu-icon\">👤</view>\n          <text class=\"menu-title\">{{ getPersonalInfoTitle() }}</text>\n          <view class=\"menu-arrow\">></view>\n        </view>\n        \n        <view v-if=\"userStore.isApproved\" class=\"menu-item\" @tap=\"goToCertificates\">\n          <view class=\"menu-icon\">🏆</view>\n          <text class=\"menu-title\">证书管理</text>\n          <view class=\"menu-arrow\">></view>\n        </view>\n      </view>\n      \n      <!-- 通用功能 -->\n      <view class=\"menu-group\">\n        <view class=\"menu-item\" @tap=\"goToFeedback\">\n          <view class=\"menu-icon\">💬</view>\n          <text class=\"menu-title\">投诉与建议</text>\n          <view class=\"menu-arrow\">></view>\n        </view>\n        \n        <view class=\"menu-item\" @tap=\"goToAbout\">\n          <view class=\"menu-icon\">ℹ️</view>\n          <text class=\"menu-title\">关于我们</text>\n          <view class=\"menu-arrow\">></view>\n        </view>\n      </view>\n      \n      <!-- 退出登录 -->\n      <view class=\"menu-group\">\n        <view class=\"menu-item logout-item\" @tap=\"handleLogout\">\n          <view class=\"menu-icon\">🚪</view>\n          <text class=\"menu-title\">退出登录</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 版本信息 -->\n    <view class=\"version-info\">\n      <text class=\"version-text\">版本 1.0.0</text>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { useUserStore } from '../../src/stores/modules/user';\nimport { storeToRefs } from 'pinia';\n\nconst userStore = useUserStore();\nconst { profile, userStatus, isApproved, isNew, isPendingReview, isRejected } = storeToRefs(userStore);\n\n/**\n * 获取状态文本\n */\nfunction getStatusText() {\n  switch (userStore.userStatus) {\n    case 'approved':\n      return '已认证';\n    case 'pending_review':\n      return '审核中';\n    case 'rejected':\n      return '审核未通过';\n    case 'new':\n      return '未完善资料';\n    default:\n      return '未知状态';\n  }\n}\n\n/**\n * 获取个人信息菜单标题\n */\nfunction getPersonalInfoTitle() {\n  if (userStore.isNew) {\n    return '完善个人资料';\n  } else if (userStore.isRejected) {\n    return '修改个人资料';\n  } else {\n    return '个人信息';\n  }\n}\n\n/**\n * 跳转到个人信息页面\n */\nfunction goToPersonalInfo() {\n  if (userStore.isApproved) {\n    uni.navigateTo({ url: '/pages/profile/personal-info' });\n  } else {\n    uni.navigateTo({ url: '/pages/register/register' });\n  }\n}\n\n/**\n * 跳转到证书管理\n */\nfunction goToCertificates() {\n  uni.navigateTo({ url: '/pages/profile/certificates' });\n}\n\n/**\n * 跳转到投诉建议\n */\nfunction goToFeedback() {\n  uni.navigateTo({ url: '/pages/profile/feedback' });\n}\n\n/**\n * 跳转到关于我们\n */\nfunction goToAbout() {\n  uni.navigateTo({ url: '/pages/profile/about' });\n}\n\n/**\n * 退出登录\n */\nfunction handleLogout() {\n  uni.showModal({\n    title: '确认退出',\n    content: '确定要退出登录吗？',\n    success: (res) => {\n      if (res.confirm) {\n        userStore.clearProfile();\n        uni.reLaunch({ url: '/pages/login/login' });\n      }\n    },\n  });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/src/styles/variables.scss';\n\n.profile-container {\n  min-height: 100vh;\n  background-color: $background-color;\n}\n\n.profile-header {\n  background: linear-gradient(135deg, $primary-color, $primary-light);\n  padding: $spacing-xl $spacing-lg;\n  display: flex;\n  align-items: center;\n  \n  .avatar {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 50%;\n    margin-right: $spacing-lg;\n    border: 4rpx solid rgba(255, 255, 255, 0.3);\n  }\n  \n  .user-info {\n    flex: 1;\n    \n    .nickname {\n      display: block;\n      font-size: $font-size-xl;\n      font-weight: $font-weight-medium;\n      color: white;\n      margin-bottom: $spacing-sm;\n    }\n    \n    .status-badge {\n      display: inline-block;\n      padding: 4rpx 12rpx;\n      border-radius: $border-radius-small;\n      font-size: $font-size-xs;\n      color: white;\n      \n      &.approved {\n        background-color: $success-color;\n      }\n      \n      &.pending {\n        background-color: $warning-color;\n      }\n      \n      &.rejected {\n        background-color: $error-color;\n      }\n      \n      &.incomplete {\n        background-color: $text-disabled;\n      }\n    }\n  }\n}\n\n.menu-section {\n  padding: $spacing-md;\n  \n  .menu-group {\n    background-color: $surface-color;\n    border-radius: $border-radius-medium;\n    margin-bottom: $spacing-md;\n    overflow: hidden;\n    box-shadow: $shadow-light;\n    \n    .menu-item {\n      display: flex;\n      align-items: center;\n      padding: $spacing-lg;\n      border-bottom: 1rpx solid $divider-color;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      &.logout-item {\n        .menu-title {\n          color: $error-color;\n        }\n      }\n      \n      .menu-icon {\n        font-size: $font-size-xl;\n        margin-right: $spacing-md;\n      }\n      \n      .menu-title {\n        flex: 1;\n        font-size: $font-size-md;\n        color: $text-primary;\n      }\n      \n      .menu-arrow {\n        font-size: $font-size-lg;\n        color: $text-disabled;\n      }\n    }\n  }\n}\n\n.version-info {\n  text-align: center;\n  padding: $spacing-lg;\n  \n  .version-text {\n    font-size: $font-size-sm;\n    color: $text-disabled;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/profile/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "uni"], "mappings": ";;;;;;AAiEA,UAAM,YAAYA,wBAAAA;AAC8DC,kBAAAA,YAAY,SAAS;AAKrG,aAAS,gBAAgB;AACvB,cAAQ,UAAU,YAAY;AAAA,QAC5B,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MACX;AAAA,IACF;AAKA,aAAS,uBAAuB;AAC9B,UAAI,UAAU,OAAO;AACZ,eAAA;AAAA,MAAA,WACE,UAAU,YAAY;AACxB,eAAA;AAAA,MAAA,OACF;AACE,eAAA;AAAA,MACT;AAAA,IACF;AAKA,aAAS,mBAAmB;AAC1B,UAAI,UAAU,YAAY;AACxBC,sBAAAA,MAAI,WAAW,EAAE,KAAK,+BAAgC,CAAA;AAAA,MAAA,OACjD;AACLA,sBAAAA,MAAI,WAAW,EAAE,KAAK,2BAA4B,CAAA;AAAA,MACpD;AAAA,IACF;AAKA,aAAS,mBAAmB;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,KAAK,8BAA+B,CAAA;AAAA,IACvD;AAKA,aAAS,eAAe;AACtBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,0BAA2B,CAAA;AAAA,IACnD;AAKA,aAAS,YAAY;AACnBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,uBAAwB,CAAA;AAAA,IAChD;AAKA,aAAS,eAAe;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,sBAAU,aAAa;AACvBA,0BAAAA,MAAI,SAAS,EAAE,KAAK,qBAAsB,CAAA;AAAA,UAC5C;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;AChJA,GAAG,WAAW,eAAe;"}